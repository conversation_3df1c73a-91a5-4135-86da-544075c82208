

```
fabric -y "https://www.youtube.com/watch?v=V7VZ91bg_Go" --stream --pattern extract_wisdom | fabric -sp translate -v=lang_code:"de" 
```

```
fabric -y "https://www.youtube.com/watch?v=V7VZ91bg_Go" --metadata --stream
```

```
fabric -y https://www.youtube.com/watch?v=V7VZ91bg_Go --transscript-with-timestamp > test.txt
```

// Generate MD file with metadata title as filename

**Working Windows Solutions:**

**PowerShell (Recommended):**
```powershell
.\get-youtube-transscript.ps1 -Url "https://www.youtube.com/watch?v=V7VZ91bg_Go"
```

**Command Prompt (CMD):**
```cmd
c:\Projects\narc\get-youtube-transscript.ps1 "https://www.youtube.com/watch?v=V7VZ91bg_Go"
```
