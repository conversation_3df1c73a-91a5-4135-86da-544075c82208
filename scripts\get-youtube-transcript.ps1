# Working PowerShell script to generate MD files with metadata title as filename
param(
    [string]$Url = "https://www.youtube.com/watch?v=V7VZ91bg_Go",
    [string]$FabricPath = "C:\Projects\fabric\fabric.exe"
)

# Set console encoding to UTF-8 for proper German character display
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=== YouTube to MD Generator ===" -ForegroundColor Green
Write-Host "Processing URL: $Url" -ForegroundColor Yellow

# Check if fabric exists
if (-not (Test-Path $FabricPath)) {
    Write-Host "Fehler: Fabric nicht gefunden bei $FabricPath" -ForegroundColor Red
    exit 1
}

# Get real metadata from fabric
Write-Host "Fetching metadata..." -ForegroundColor Yellow
$metadataJson = & $FabricPath -y $Url --metadata --stream 2>$null

if (-not $metadataJson) {
    Write-Host "Error: Failed to fetch metadata from fabric. Make sure fabric is installed and the URL is valid." -ForegroundColor Red
    exit 1
}

# Parse the JSON
$metadata = $metadataJson | ConvertFrom-Json

# Extract fields with fallback handling
$title = if ($metadata.title) { $metadata.title } else { "Unknown_Video" }
$channelTitle = if ($metadata.channelTitle) { $metadata.channelTitle } else { if ($metadata.channel) { $metadata.channel } else { "Unknown_Channel" } }
$channelHandle = $channelTitle -replace '[^\w]', ''
$channelLink = "https://www.youtube.com/@$channelHandle"
$publishedAt = if ($metadata.publishedAt) { [string]$metadata.publishedAt } else { [string]$metadata.published }
$description = if ($metadata.description) { $metadata.description } else { "No description available" }

# Clean up published date for filename
# Ensure publishedAt is always a string
if ($publishedAt -match '^\d{4}-\d{2}-\d{2}') {
    $publishedDate = ($publishedAt -split 'T')[0]
} else {
    try {
        $publishedDate = Get-Date $publishedAt -Format 'yyyy-MM-dd'
    } catch {
        $publishedDate = Get-Date -Format 'yyyy-MM-dd'
    }
}
$title = $title -replace '[^\w\s-]', '_'
$title = $title -replace '\s+', '_'
$title = $title -replace '_+', '_'
$mdDir = "youtube/transcripts"
$logDir = "youtube/transcripts/log"
$baseFilename = "${publishedDate}_$title"
$mdPath = Join-Path $mdDir "$baseFilename.md"
$jsonPath = Join-Path $logDir "$baseFilename.json"
Write-Host "Generated filename: $mdPath" -ForegroundColor Cyan

# Get content from fabric
Write-Host "Extracting content..." -ForegroundColor Yellow
$summary = & $FabricPath -y $Url --stream --pattern extract_wisdom 2>$null
$transcript = & $FabricPath -y $Url --transcript 2>$null

# Create comprehensive MD content
$content = @"
# $($title)

**Video Information:**
- **URL:** $Url
- **Channel:** [$channelTitle]($channelLink)
- **Published:** $($publishedDate)
- **Description:** $($description)

## Video Summary

$summary

## Full Transcript

$transcript

---

*Generated on $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')*
*Using fabric AI tools for content extraction*
"@

# Save MD file
$content | Out-File -FilePath $mdPath -Encoding UTF8

# Add generated_on as the first field in metadata JSON
$generatedOn = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
$metadataWithDate = [ordered]@{ generated_on = $generatedOn }
$metadata.PSObject.Properties | ForEach-Object { $metadataWithDate[$_.Name] = $_.Value }
$metadataWithDate | ConvertTo-Json -Depth 10 | Out-File -FilePath $jsonPath -Encoding UTF8

if (Test-Path $mdPath) {
    Write-Host "✅ Successfully created: $mdPath" -ForegroundColor Green
    Write-Host "📄 File size: $((Get-Item $mdPath).Length) bytes"
    
    # Show first few lines
    Write-Host "`n📖 Preview of content:" -ForegroundColor Yellow
    Get-Content $mdPath -TotalCount 5
} else {
    Write-Host "❌ Failed to create file" -ForegroundColor Red
}

