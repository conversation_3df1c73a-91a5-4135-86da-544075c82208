# Process all unchecked YouTube videos from Videos.md
param(
    [string]$VideosFile = "youtube/Videos.md",
    [string]$GetTranscriptScript = "scripts/get-youtube-transcript.ps1",
    [string]$FabricPath = "C:\Projects\fabric\fabric.exe"
)

# Set console encoding to UTF-8 for proper German character display
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=== YouTube Video Batch Processor ===" -ForegroundColor Green
Write-Host "Processing videos from: $VideosFile" -ForegroundColor Yellow

# Check if Videos.md exists
if (-not (Test-Path $VideosFile)) {
    Write-Host "Fehler: Videos.md nicht gefunden bei $VideosFile" -ForegroundColor Red
    exit 1
}

# Check if get-youtube-transcript.ps1 exists
if (-not (Test-Path $GetTranscriptScript)) {
    Write-Host "Fehler: get-youtube-transcript.ps1 nicht gefunden bei $GetTranscriptScript" -ForegroundColor Red
    exit 1
}

# Read Videos.md content
$content = Get-Content $VideosFile -Raw -Encoding UTF8

# Find all unchecked YouTube URLs
$uncheckedPattern = '- \[ \] (https://www\.youtube\.com/watch\?v=([a-zA-Z0-9_-]+))'
$uncheckedMatches = [regex]::Matches($content, $uncheckedPattern)

if ($uncheckedMatches.Count -eq 0) {
    Write-Host "Keine unverarbeiteten Videos gefunden." -ForegroundColor Yellow
    exit 0
}

Write-Host "Gefunden $($uncheckedMatches.Count) unverarbeitete Videos:" -ForegroundColor Cyan

$processedCount = 0
$failedCount = 0

foreach ($match in $uncheckedMatches) {
    $url = $match.Groups[1].Value
    $videoId = $match.Groups[2].Value
    
    Write-Host "`nVerarbeite Video: $url" -ForegroundColor Yellow
    Write-Host "Video ID: $videoId" -ForegroundColor Gray
    
    try {
        # Run get-youtube-transcript.ps1 for this URL
        Write-Host "-> Lade Transkript herunter..." -ForegroundColor Gray
        $transcriptStart = Get-Date
        
        & $GetTranscriptScript -Url $url
        
        $transcriptEnd = Get-Date
        $transcriptDuration = ($transcriptEnd - $transcriptStart).TotalSeconds
        
        # Check if transcript was created successfully
        $transcriptFiles = Get-ChildItem -Path "youtube/transcripts" -Filter "*.md" | Where-Object { $_.Name -like "*$videoId*" -or $_.LastWriteTime -gt $transcriptStart }
        
        if ($transcriptFiles.Count -gt 0) {
            Write-Host "Erfolgreich: Transkript erstellt in $([math]::Round($transcriptDuration, 1))s" -ForegroundColor Green
            
            # Mark as completed in Videos.md
            $oldPattern = "- \[ \] $([regex]::Escape($url))"
            $newPattern = "- [x] $url"
            $content = $content -replace $oldPattern, $newPattern
            
            $processedCount++
        } else {
            Write-Host "Fehler: Kein Transkript gefunden nach $([math]::Round($transcriptDuration, 1))s" -ForegroundColor Red
            $failedCount++
        }
        
    } catch {
        Write-Host "Fehler beim Verarbeiten von $url : $($_.Exception.Message)" -ForegroundColor Red
        $failedCount++
    }
}

# Save updated Videos.md
if ($processedCount -gt 0) {
    $content | Out-File -FilePath $VideosFile -Encoding UTF8
    Write-Host "`nVideos.md aktualisiert mit $processedCount Häkchen." -ForegroundColor Green
}

# Summary
Write-Host "`n=== Zusammenfassung ===" -ForegroundColor Green
Write-Host "Erfolgreich verarbeitet: $processedCount" -ForegroundColor Green
Write-Host "Fehlgeschlagen: $failedCount" -ForegroundColor Red
Write-Host "Gesamt: $($uncheckedMatches.Count)" -ForegroundColor Cyan

if ($processedCount -gt 0) {
    Write-Host "`nTipp: Führe jetzt 'scripts/process-transcripts-wisdom.ps1' aus, um Weisheits-Extrakte zu erstellen." -ForegroundColor Yellow
}
