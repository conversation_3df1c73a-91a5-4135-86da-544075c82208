# Process all transcript MD files and extract wisdom
param(
    [string]$TranscriptDir = "youtube/transcripts",
    [string]$OutputDir = "wisdom",
    [string]$Language = "de"
)

Write-Host "=== Transcript Wisdom Processor ===" -ForegroundColor Green
Write-Host "Processing transcripts from: $TranscriptDir" -ForegroundColor Yellow

# Create output directory if it doesn't exist
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# Get all MD files in transcript directory
$mdFiles = Get-ChildItem -Path $TranscriptDir -Filter "*.md" -File

if ($mdFiles.Count -eq 0) {
    Write-Host "No MD files found in $TranscriptDir" -ForegroundColor Red
    exit 1
}

Write-Host "Found $($mdFiles.Count) transcript files to process" -ForegroundColor Cyan

foreach ($file in $mdFiles) {
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
    
    Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
    
    try {
        # Extract wisdom from the MD file content
        $wisdom = Get-Content $file.FullName -Raw | fabric --stream --pattern extract_wisdom
        
        # Translate wisdom to German
        $germanWisdom = $wisdom | fabric -sp translate -v=lang_code:"$Language"
        
        # Extract title from wisdom for German filename
        $germanTitle = ($germanWisdom -split "`n" | Where-Object { $_ -match "^#" } | Select-Object -First 1) -replace "^#\s*", ""
        if (-not $germanTitle) {
            $germanTitle = "Weisheit_Extrakt"
        }
        
        # Clean German title for filename
        $germanTitle = $germanTitle -replace '[^\w\s-]', '_'
        $germanTitle = $germanTitle -replace '\s+', '_'
        $germanTitle = $germanTitle -replace '_+', '_'
        
        # Extract date from original filename (assuming format: YYYY-MM-DD_Title)
        $dateMatch = $baseName -match '^(\d{4}-\d{2}-\d{2})'
        $datePrefix = if ($dateMatch) { $Matches[1] } else { Get-Date -Format 'yyyy-MM-dd' }
        
        $outputFile = Join-Path $OutputDir "$datePrefix`_$germanTitle.md"
        
        # Create output content with header
        $content = @"
# $germanTitle

**Quelle:** $($file.Name)
**Erstellt:** $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
**Sprache:** Deutsch

---

$germanWisdom
"@
        
        # Save wisdom extract
        $content | Out-File -FilePath $outputFile -Encoding UTF8
        
        Write-Host "✅ Created: $outputFile" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Failed to process $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 Processing complete! Check $OutputDir for wisdom extracts." -ForegroundColor Green