# Process all transcript MD files and extract wisdom
param(
    [string]$TranscriptDir = "youtube/transcripts",
    [string]$OutputDir = "youtube/wisdom",
    [string]$Language = "de",
    [string]$FabricPath = "C:\Projects\fabric\fabric.exe"
)

# Set console encoding to UTF-8 for proper German character display
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=== Transcript Wisdom Processor ===" -ForegroundColor Green
Write-Host "Processing transcripts from: $TranscriptDir" -ForegroundColor Yellow

# Check if fabric exists
if (-not (Test-Path $FabricPath)) {
    Write-Host "Error: Fabric not found at $FabricPath" -ForegroundColor Red
    Write-Host "Please ensure fabric is installed or update the FabricPath parameter." -ForegroundColor Red
    exit 1
}

Write-Host "Using fabric at: $FabricPath" -ForegroundColor Cyan

# Create output directory if it doesn't exist
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# Get all MD files in transcript directory
$mdFiles = Get-ChildItem -Path $TranscriptDir -Filter "*.md" -File

if ($mdFiles.Count -eq 0) {
    Write-Host "No MD files found in $TranscriptDir" -ForegroundColor Red
    exit 1
}

Write-Host "Found $($mdFiles.Count) transcript files to process" -ForegroundColor Cyan

foreach ($file in $mdFiles) {
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
    
    Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
    
    try {
        # Extract wisdom from the MD file content
        Write-Host "  -> Extracting wisdom with fabric..." -ForegroundColor Gray
        $extractStart = Get-Date
        $wisdom = Get-Content $file.FullName -Raw | & $FabricPath --stream --pattern extract_wisdom
        $extractEnd = Get-Date
        $extractDuration = ($extractEnd - $extractStart).TotalSeconds

        if (-not $wisdom) {
            Write-Host "  Warnung: No wisdom extracted, skipping translation..." -ForegroundColor Yellow
            continue
        }

        Write-Host "  Erfolg: Wisdom extracted successfully in $([math]::Round($extractDuration, 1))s, translating to $Language..." -ForegroundColor Gray
        $translateStart = Get-Date
        # Translate wisdom to German
        $germanWisdom = $wisdom | & $FabricPath -sp translate -v=lang_code:"$Language"
        $translateEnd = Get-Date
        $translateDuration = ($translateEnd - $translateStart).TotalSeconds
        
        # Use the same filename as original but with German content
        # Keep the original filename structure for consistency
        $outputFile = Join-Path $OutputDir "$baseName.md"
        
        # Create output content with header
        $content = @"
# $($baseName.Replace('_', ' '))

**Quelle:** $($file.Name)
**Erstellt:** $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
**Sprache:** Deutsch
**Verarbeitungszeit:** Extraktion: $([math]::Round($extractDuration, 1))s, Übersetzung: $([math]::Round($translateDuration, 1))s

---

$germanWisdom
"@

        # Save wisdom extract with UTF-8 encoding for German characters
        $content | Out-File -FilePath $outputFile -Encoding UTF8

        Write-Host "Erfolgreich erstellt: $outputFile (Extract: $([math]::Round($extractDuration, 1))s, Translate: $([math]::Round($translateDuration, 1))s)" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Failed to process $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nVerarbeitung abgeschlossen! Prüfe $OutputDir für Weisheits-Extrakte." -ForegroundColor Green